import 'dart:async';
import 'package:get/get.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:rolio/common/services/session_binding_service.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:rolio/manager/ws_manager.dart';
import 'package:rolio/manager/ws_message_manager.dart'; // 添加WsMsg类的导入
import 'package:rolio/modules/chat/service/message_service.dart'; // 添加MessageService的导入

/// AI频道管理器
///
/// 负责管理多个AI角色的频道订阅和消息处理
/// 解决不同角色消息的接收和处理问题
/// 作为唯一的WebSocket消息订阅中心
class AiChannelManager extends GetxService {
  // 依赖服务
  final WsManager _wsManager = Get.find<WsManager>();
  // EventBus已迁移到GetX响应式机制，不再需要

  // Worker管理
  final List<Worker> _workers = [];

  // 消息跟踪器
  final MessageTracker _messageTracker = MessageTracker();

  // 消息去重缓存
  final Set<String> _processedMessageIds = <String>{};

  // 角色订阅管理
  final Map<int, CompositeSubscription> _roleSubscriptions = {};

  // 角色会话绑定缓存
  final Map<int, int> _roleConversationBindings = {};

  // 当前活跃角色ID
  final RxInt activeRoleId = RxInt(0);

  // 是否已初始化
  bool _isInitialized = false;

  // 标记事件监听器是否已设置
  bool _eventListenersSetup = false;

  // 定期清理Worker - 使用GetX
  Worker? _cleanupWorker;

  @override
  void onInit() {
    super.onInit();
    _initialize();

    // 设置定期清理任务 - 每30秒清理一次消息缓存
    _setupCleanupTask();
  }

  /// 设置定期清理任务 - 使用Stream.periodic
  void _setupCleanupTask() {
    // 取消现有Worker
    _cleanupWorker?.dispose();

    // 使用Stream.periodic创建周期性清理任务
    final cleanupStream = Stream.periodic(
      const Duration(seconds: 30),
      (count) => count,
    );

    // 直接监听清理流
    cleanupStream.listen((_) => _cleanupCache());

    LogUtil.debug('已设置定期清理Stream, 间隔30秒');
  }

  /// 清理缓存
  void _cleanupCache() {
    try {
      // 清理消息去重缓存
      _processedMessageIds.clear();
      LogUtil.debug('已清理消息去重缓存');
    } catch (e) {
      LogUtil.error('清理缓存失败: $e');
    }
  }

  /// 初始化
  void _initialize() {
    if (_isInitialized) return;

    // 设置事件监听器
    _setupEventListeners();

    // 监听角色会话绑定事件
    _workers.add(ever(GlobalEventState.to.roleConversationBound, (Map<String, dynamic>? data) {
      if (data != null &&
          data.containsKey('role_id') &&
          data.containsKey('conversation_id')) {
        final roleId = data['role_id'] as int;
        final conversationId = data['conversation_id'] as int;

        // 更新角色会话绑定缓存
        _roleConversationBindings[roleId] = conversationId;
        LogUtil.debug(
            '收到角色会话绑定事件: roleId=$roleId, conversationId=$conversationId');

        // 尝试订阅该角色
        subscribeToRole(roleId);
      }
    }));

    // 监听WebSocket连接状态
    _wsManager.connectionStateStream.listen((state) {
      if (state == WsConnectionState.connected) {
        // 连接成功时，重新订阅所有已知角色
        _resubscribeAllRoles();
      }
    });

    // 监听角色变更事件
    _workers.add(ever(GlobalEventState.to.activeRoleChanged, (Map<String, dynamic>? data) {
      if (data != null && data.containsKey('roleId')) {
        final roleId = data['roleId'] as int;
        if (roleId > 0) {
          setActiveRole(roleId);
        }
      }
    }));

    _isInitialized = true;
    LogUtil.info('AiChannelManager初始化完成');
  }

  /// 设置事件监听器
  void _setupEventListeners() {
    // 防止重复设置事件监听器
    if (_eventListenersSetup) {
      LogUtil.debug('AiChannelManager - 事件监听器已设置，跳过重复设置');
      return;
    }

    LogUtil.debug('AiChannelManager - 设置事件监听器');

    // 移除roleMessage事件监听器，消息处理已简化为直接调用

    // 保留对原有非活跃角色消息事件的监听，确保向后兼容
    // 重要：不再在事件处理器中触发同类型事件，避免循环引用
    // 注释：nonActiveRoleMessage事件已通过roleMessage统一处理，此监听器可能不再需要
    /*
    _workers.add(ever(GlobalEventState.to.nonActiveRoleMessage, (Map<String, dynamic>? data) {
      if (data != null) {
        final roleId = data['roleId'] as int?;
        final message = data['message'] as WsMsg?;

        // 消息去重处理
        if (roleId != null && message != null) {
          final messageKey =
              '${roleId}_${message.data.hashCode}_${DateTime.now().millisecondsSinceEpoch ~/ 2000}';
          if (_processedMessageIds.contains(messageKey)) {
            LogUtil.debug(
                'AiChannelManager - 已处理过的消息，跳过: roleId=$roleId, key=$messageKey');
            return;
          }

          LogUtil.debug('收到传统非活跃角色消息事件: roleId=$roleId');

          // 立即处理消息 - 不存储
          _processNonActiveRoleMessage(roleId, message);
        }
      }
    }));
    */

    _eventListenersSetup = true;
    LogUtil.debug('AiChannelManager - 事件监听器设置完成');
  }

  /// 设置当前活跃角色
  void setActiveRole(int roleId) {
    if (roleId <= 0) return;

    // 如果角色ID没有变化，不执行任何操作
    if (activeRoleId.value == roleId) {
      LogUtil.debug('活跃角色ID未变化，跳过设置: $roleId');
      return;
    }

    // 记录订阅前的状态
    final beforeSubscriptions = _roleSubscriptions.keys.toList();
    final beforeCount = _roleSubscriptions.length;

    LogUtil.info(
        '设置当前活跃角色: 从${activeRoleId.value}变更为$roleId (订阅前: $beforeCount 个)');
    activeRoleId.value = roleId;

    // 确保该角色已订阅
    subscribeToRole(roleId);

    // 记录订阅后的状态
    final afterSubscriptions = _roleSubscriptions.keys.toList();
    final afterCount = _roleSubscriptions.length;

    LogUtil.info('活跃角色设置完成: 角色ID=$roleId, 订阅前=$beforeCount, 订阅后=$afterCount');
    if (beforeCount != afterCount) {
      LogUtil.debug('订阅变化: 从 $beforeSubscriptions 变为 $afterSubscriptions');
    }
  }

  /// 重新订阅所有已知角色
  void _resubscribeAllRoles() {
    // 获取所有已知角色ID
    final knownRoleIds = Set<int>();
    knownRoleIds.addAll(_roleConversationBindings.keys);

    // 确保当前活跃角色也被订阅
    if (activeRoleId.value > 0) {
      knownRoleIds.add(activeRoleId.value);
    }

    // 重新订阅所有角色
    for (final roleId in knownRoleIds) {
      subscribeToRole(roleId);
    }

    LogUtil.info('已重新订阅${knownRoleIds.length}个角色');
  }

  /// 订阅角色频道
  ///
  /// [roleId] 角色ID
  /// 返回是否成功订阅
  bool subscribeToRole(int roleId) {
    // 检查参数有效性
    if (roleId <= 0) {
      LogUtil.warn('订阅角色失败：无效的角色ID: $roleId');
      return false;
    }

    // 记录当前已有的订阅情况
    final existingSubscriptionCount = _roleSubscriptions.length;
    final currentSubscriptions = _roleSubscriptions.keys.toList();

    LogUtil.debug('当前已有${existingSubscriptionCount}个订阅: $currentSubscriptions');

    // 检查是否已订阅 - 如果已订阅，直接返回true，不重新创建订阅
    if (_roleSubscriptions.containsKey(roleId)) {
      // 检查订阅是否有效
      final subscription = _roleSubscriptions[roleId];
      if (subscription != null) {
        LogUtil.debug('角色$roleId已有有效订阅，跳过重复订阅');
        return true;
      } else {
        // 只有在订阅无效时才取消重建
        LogUtil.debug('角色$roleId的订阅无效，取消并重新创建');
        unsubscribeFromRole(roleId);
      }
    }

    try {
      // 统一订阅聊天消息和AI回复消息
      final chatSubscription = _wsManager
          .onChannel(WsEvent.chat_message, roleId)
          .listen((message) =>
              _processRoleMessage(roleId, message, WsEvent.chat_message));

      final aiReplySubscription = _wsManager
          .onChannel(WsEvent.ai_reply, roleId)
          .listen((message) =>
              _processRoleMessage(roleId, message, WsEvent.ai_reply));

      // 使用CompositeSubscription来管理多个订阅
      final compositeSubscription = CompositeSubscription();
      compositeSubscription.add(chatSubscription);
      compositeSubscription.add(aiReplySubscription);

      _roleSubscriptions[roleId] = compositeSubscription;

      // 记录订阅后的情况
      final newSubscriptionCount = _roleSubscriptions.length;
      final updatedSubscriptions = _roleSubscriptions.keys.toList();

      LogUtil.info(
          '已订阅角色$roleId的所有消息类型，订阅总数：$newSubscriptionCount，当前订阅：$updatedSubscriptions');
      return true;
    } catch (e) {
      LogUtil.error('订阅角色$roleId的消息频道失败: $e');
      return false;
    }
  }

  /// 处理角色消息
  ///
  /// [roleId] 角色ID
  /// [message] 消息对象
  /// [eventType] 事件类型
  void _processRoleMessage(int roleId, WsMsg message, WsEvent eventType) {
    try {
      // 提取conversation_id
      final conversationId = message.channelId;

      // 消息去重 - 修复重复处理问题
      // 生成消息唯一标识，基于角色ID、消息内容哈希值、通道ID和事件类型
      final messageKey = '${roleId}_${message.data.hashCode}_${conversationId}_${eventType.toString()}';
      if (_processedMessageIds.contains(messageKey)) {
        LogUtil.debug('AiChannelManager - 消息已处理，跳过重复处理: roleId=$roleId, eventType=$eventType, key=$messageKey');
        return;
      }

      // 添加到已处理列表
      _processedMessageIds.add(messageKey);

      LogUtil.info('收到角色$roleId的${eventType}消息，会话ID: $conversationId');

      // 如果消息中包含conversation_id，更新绑定关系
      if (conversationId != null && conversationId > 0) {
        // 检查是否已经绑定到相同的会话
        final existingConversationId = _roleConversationBindings[roleId];
        if (existingConversationId == conversationId) {
          LogUtil.debug(
              '角色已绑定到相同会话，跳过: roleId=$roleId, conversationId=$conversationId');
        } else {
          _bindConversationToRole(roleId, conversationId);
        }
      }

      // 判断是否是当前活跃角色
      final isActiveRole = (roleId == activeRoleId.value);

      // 不再广播事件，消息处理已移至WsMessageManager直接调用MessageService
      LogUtil.debug(
          '消息处理已简化，不再广播事件: roleId=$roleId, eventType=$eventType, isActiveRole=$isActiveRole');

      // 不存储消息，处理完毕后立即释放
    } catch (e) {
      LogUtil.error('处理角色$roleId的消息失败: $e');
    }
  }

  /// 处理非活跃角色的消息
  ///
  /// [roleId] 角色ID
  /// [message] 消息对象
  void _processNonActiveRoleMessage(int roleId, WsMsg message) {
    try {
      // 生成消息唯一标识，防止处理循环
      final String messageKey =
          '${roleId}_${message.data.hashCode}_${DateTime.now().millisecondsSinceEpoch ~/ 2000}';

      // 检查是否已处理过此消息 - 防止事件循环
      if (_processedMessageIds.contains(messageKey)) {
        LogUtil.debug('已处理过的非活跃角色消息，跳过: roleId=$roleId, key=$messageKey');
        return;
      }

      // 添加到已处理列表
      _processedMessageIds.add(messageKey);

      // 提取消息中的会话ID
      final conversationId = message.channelId;

      // 提取消息内容（用于日志）
      String? content;
      if (message.data is Map && message.data.containsKey('content')) {
        content = message.data['content']?.toString();
        if (content != null && content.length > 30) {
          content = '${content.substring(0, 30)}...';
        }
      }

      LogUtil.debug(
          '处理非活跃角色消息: roleId=$roleId, channelId=$conversationId, content=$content');

      // 如果消息中包含conversation_id，立即更新绑定关系
      if (conversationId != null && conversationId > 0) {
        // 检查是否已经绑定到相同会话，避免重复绑定
        final existingConversationId = _roleConversationBindings[roleId];
        if (existingConversationId == conversationId) {
          LogUtil.debug(
              '角色已绑定到相同会话，跳过: roleId=$roleId, conversationId=$conversationId');
        } else {
          _bindConversationToRole(roleId, conversationId);
          LogUtil.debug(
              '已绑定非活跃角色会话: roleId=$roleId, conversationId=$conversationId');
        }
      }

      // 关键修复：处理非活跃角色的待处理消息
      _processNonActiveRolePendingMessages(roleId, message);

      // 处理完成，不存储消息
      LogUtil.debug('完成非活跃角色消息处理: roleId=$roleId');
    } catch (e) {
      LogUtil.error('处理非活跃角色消息失败: $e');
    }
  }

  /// 处理非活跃角色的待处理消息
  ///
  /// [roleId] 角色ID
  /// [message] WebSocket消息
  void _processNonActiveRolePendingMessages(int roleId, WsMsg message) {
    try {
      // 获取MessageService实例来处理待处理消息
      if (Get.isRegistered<MessageService>()) {
        final messageService = Get.find<MessageService>();

        // 确保消息数据中包含事件类型
        final messageData = Map<String, dynamic>.from(message.data);
        messageData['event'] = message.event.toString();

        // 确保消息包含conversation_id
        if (message.conversationid != null) {
          messageData['conversation_id'] = message.conversationid;
        } else if (message.channelId != null) {
          messageData['conversation_id'] = message.channelId;
        }

        // 确保消息包含role_id
        if (!messageData.containsKey('role_id')) {
          messageData['role_id'] = roleId;
        }

        LogUtil.info(
            '处理非活跃角色$roleId的待处理消息，会话ID: ${messageData['conversation_id']}');

        // 调用MessageService的processAiMessage方法处理待处理消息
        messageService.processAiMessage(messageData);

        LogUtil.debug('已将非活跃角色$roleId的消息交给MessageService处理');
      } else {
        LogUtil.warn('MessageService未注册，无法处理非活跃角色$roleId的待处理消息');
      }
    } catch (e) {
      LogUtil.error('处理非活跃角色$roleId的待处理消息失败: $e');
    }
  }

  /// 绑定会话ID到角色（私有方法）
  ///
  /// [roleId] 角色ID
  /// [conversationId] 会话ID
  void _bindConversationToRole(int roleId, int conversationId) {
    // 更新内部缓存
    _roleConversationBindings[roleId] = conversationId;

    // 如果已注册SessionBindingService，使用它进行绑定
    if (Get.isRegistered<SessionBindingService>()) {
      final bindingService = Get.find<SessionBindingService>();
      bindingService.bindRoleToConversation(roleId, conversationId);
      LogUtil.info('已绑定会话$conversationId到角色$roleId');
    }
  }

  /// 绑定会话ID到角色（公共方法）
  ///
  /// [roleId] 角色ID
  /// [conversationId] 会话ID
  void bindRoleToConversation(int roleId, int conversationId) {
    _bindConversationToRole(roleId, conversationId);
  }

  /// 获取角色消息
  ///
  /// [roleId] 角色ID
  /// 返回空列表 - 不再保存消息
  List<WsMsg> getRoleMessages(int roleId) {
    // 返回空列表，因为我们不存储消息
    return [];
  }

  /// 清除角色消息缓冲区
  ///
  /// [roleId] 角色ID
  void clearRoleMessageBuffer(int roleId) {
    // 不执行任何操作，因为没有消息缓冲区
    LogUtil.debug('角色$roleId没有消息缓存需要清理');
  }

  /// 取消订阅角色
  ///
  /// [roleId] 角色ID
  void unsubscribeFromRole(int roleId) {
    if (_roleSubscriptions.containsKey(roleId)) {
      LogUtil.info('正在取消订阅角色$roleId');

      // 取消WebSocket订阅
      _roleSubscriptions[roleId]?.cancel();
      _roleSubscriptions.remove(roleId);
      
      // 同时清除角色会话绑定缓存
      _roleConversationBindings.remove(roleId);
      
      // 同步清除MessageTracker中的角色订阅
      try {
        final messageTracker = MessageTracker();
        // 先清理该角色的待处理消息
        int removedCount = messageTracker.clearPendingMessagesByRoleId(roleId);
        if (removedCount > 0) {
          LogUtil.debug('已清除角色$roleId的$removedCount条待处理消息');
        }
        // 再移除角色订阅
        messageTracker.removeRoleSubscription(roleId);
        LogUtil.debug('已同步清除MessageTracker中角色$roleId的订阅');
      } catch (e) {
        LogUtil.error('清除MessageTracker中角色$roleId的订阅失败: $e');
      }
      
      // 清除WsMessageManager中的通道处理器
      try {
        if (Get.isRegistered<WsMessageManager>()) {
          final wsMessageManager = Get.find<WsMessageManager>();
          wsMessageManager.clearChannelHandlers(roleId);
          LogUtil.debug('已清除WsMessageManager中角色$roleId的通道处理器');
        }
      } catch (e) {
        LogUtil.error('清除WsMessageManager中角色$roleId的通道处理器失败: $e');
      }

      // 记录取消订阅后的情况
      final remainingSubscriptions = _roleSubscriptions.keys.toList();
      LogUtil.info('已取消订阅角色$roleId，剩余订阅：${_roleSubscriptions.length}，角色：$remainingSubscriptions');
    } else {
      LogUtil.debug('角色$roleId没有活跃订阅，无需取消');
    }
  }

  /// 订阅多个角色
  ///
  /// [roleIds] 角色ID列表
  void subscribeToMultipleRoles(List<int> roleIds) {
    // 记录已订阅的角色和新订阅的角色
    final existingRoleIds = _roleSubscriptions.keys.toList();
    final newRoleIds = <int>[];
    LogUtil.info('订阅多个角色: $roleIds');
    for (final roleId in roleIds) {
      if (!_roleSubscriptions.containsKey(roleId)) {
        newRoleIds.add(roleId);
      }
      subscribeToRole(roleId);
    }

    // 如果有新订阅的角色，记录日志
    if (newRoleIds.isNotEmpty) {
      LogUtil.info('新订阅了${newRoleIds.length}个角色: $newRoleIds');
    }
  }

  /// 获取所有已订阅的角色ID
  ///
  /// 返回已订阅的角色ID列表
  List<int> getSubscribedRoleIds() {
    return _roleSubscriptions.keys.toList();
  }

  /// 获取角色的会话ID
  ///
  /// [roleId] 角色ID
  /// 返回会话ID，如果没有则返回null
  int? getConversationIdForRole(int roleId) {
    return _roleConversationBindings[roleId];
  }

  /// 清除所有缓存
  void clearAllCache() {
    // 清理消息去重缓存
    _processedMessageIds.clear();
    
    // 清理角色订阅记录
    final subscribedRoles = _roleSubscriptions.keys.toList();
    if(subscribedRoles.isNotEmpty) {
      LogUtil.debug('清理所有角色订阅，之前的订阅: $subscribedRoles');
    }
    
    // 取消所有订阅
    for (final subscription in _roleSubscriptions.values) {
      subscription.cancel();
    }
    _roleSubscriptions.clear();
    
    // 清理角色会话绑定缓存
    _roleConversationBindings.clear();
    
    // 同步清理WsMessageManager中的通道处理器
    try {
      if (Get.isRegistered<WsMessageManager>()) {
        final wsMessageManager = Get.find<WsMessageManager>();
        
        // 清理之前记录的所有角色订阅
        for (final roleId in subscribedRoles) {
          wsMessageManager.clearChannelHandlers(roleId);
          LogUtil.debug('已清理WsMessageManager中角色$roleId的通道处理器');
        }
      }
    } catch (e) {
      LogUtil.error('清理WsMessageManager中角色通道处理器失败: $e');
    }
    
    LogUtil.debug('已清除消息去重缓存、角色订阅和角色会话绑定');
  }

  @override
  void onClose() {
    // 取消定期清理任务
    _cleanupWorker?.dispose();

    // 清理Workers
    for (var worker in _workers) {
      worker.dispose();
    }
    _workers.clear();

    // 取消所有订阅
    for (final subscription in _roleSubscriptions.values) {
      subscription.cancel();
    }
    _roleSubscriptions.clear();

    super.onClose();
  }
}

/// 复合订阅
/// 用于管理多个StreamSubscription
class CompositeSubscription {
  final List<StreamSubscription> _subscriptions = [];

  void add(StreamSubscription subscription) {
    _subscriptions.add(subscription);
  }

  Future<void> cancel() async {
    for (final subscription in _subscriptions) {
      await subscription.cancel();
    }
    _subscriptions.clear();
  }

  void pause([Future<void>? resumeSignal]) {
    for (final subscription in _subscriptions) {
      subscription.pause(resumeSignal);
    }
  }

  void resume() {
    for (final subscription in _subscriptions) {
      subscription.resume();
    }
  }

  bool get isPaused =>
      _subscriptions.isEmpty ? false : _subscriptions.first.isPaused;
}
