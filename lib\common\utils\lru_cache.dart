// LRU缓存工具类

/// LRU缓存节点
/// 
/// 双向链表节点，用于维护访问顺序
class LRUCacheNode<K, V> {
  /// 缓存键
  K key;
  
  /// 缓存值
  V value;
  
  /// 创建时间戳
  final int createTime;
  
  /// 过期时间戳（可选）
  int? expireTime;
  
  /// 上一个节点
  LRUCacheNode<K, V>? prev;
  
  /// 下一个节点
  LRUCacheNode<K, V>? next;
  
  /// 构造函数
  LRUCacheNode({
    required this.key,
    required this.value,
    this.expireTime,
  }) : createTime = DateTime.now().millisecondsSinceEpoch;
  
  /// 检查是否过期
  bool get isExpired {
    if (expireTime == null) return false;
    return DateTime.now().millisecondsSinceEpoch > expireTime!;
  }
  
  /// 获取剩余生存时间（毫秒）
  int get remainingTtl {
    if (expireTime == null) return -1;
    final remaining = expireTime! - DateTime.now().millisecondsSinceEpoch;
    return remaining > 0 ? remaining : 0;
  }
}

/// LRU缓存统计信息
class LRUCacheStats {
  /// 总访问次数
  int totalAccess = 0;
  
  /// 命中次数
  int hitCount = 0;
  
  /// 未命中次数
  int missCount = 0;
  
  /// 过期清理次数
  int expiredCleanCount = 0;
  
  /// LRU淘汰次数
  int evictionCount = 0;
  
  /// 命中率
  double get hitRate {
    if (totalAccess == 0) return 0.0;
    return hitCount / totalAccess;
  }
  
  /// 重置统计
  void reset() {
    totalAccess = 0;
    hitCount = 0;
    missCount = 0;
    expiredCleanCount = 0;
    evictionCount = 0;
  }
  
  @override
  String toString() {
    return 'LRUCacheStats{总访问: $totalAccess, 命中: $hitCount, 未命中: $missCount, '
           '命中率: ${(hitRate * 100).toStringAsFixed(2)}%, 淘汰: $evictionCount, 过期清理: $expiredCleanCount}';
  }
}

/// 通用LRU缓存实现
/// 
/// 支持泛型键值对，自动过期，统计信息等功能
class LRUCache<K, V> {
  /// 最大容量
  final int maxCapacity;
  
  /// 默认TTL（毫秒），null表示永不过期
  final int? defaultTtl;
  
  /// 是否启用统计
  final bool enableStats;
  
  /// 缓存映射表
  final Map<K, LRUCacheNode<K, V>> _cache = <K, LRUCacheNode<K, V>>{};
  
  /// 双向链表头节点（最近使用）
  late final LRUCacheNode<K, V> _head;
  
  /// 双向链表尾节点（最久未使用）
  late final LRUCacheNode<K, V> _tail;
  
  /// 统计信息
  final LRUCacheStats _stats = LRUCacheStats();
  
  /// 构造函数
  LRUCache({
    required this.maxCapacity,
    this.defaultTtl,
    this.enableStats = true,
  }) {
    if (maxCapacity <= 0) {
      throw ArgumentError('maxCapacity must be positive');
    }
    
    // 初始化哨兵节点
    _head = LRUCacheNode<K, V>(key: null as K, value: null as V);
    _tail = LRUCacheNode<K, V>(key: null as K, value: null as V);
    _head.next = _tail;
    _tail.prev = _head;
  }
  
  /// 获取缓存值
  V? get(K key) {
    if (enableStats) _stats.totalAccess++;
    
    final node = _cache[key];
    if (node == null) {
      if (enableStats) _stats.missCount++;
      return null;
    }
    
    // 检查是否过期
    if (node.isExpired) {
      _removeNode(node);
      _cache.remove(key);
      if (enableStats) {
        _stats.missCount++;
        _stats.expiredCleanCount++;
      }
      return null;
    }
    
    // 移动到头部（标记为最近使用）
    _moveToHead(node);
    
    if (enableStats) _stats.hitCount++;
    return node.value;
  }
  
  /// 设置缓存值
  void put(K key, V value, {int? ttl}) {
    final existingNode = _cache[key];
    
    if (existingNode != null) {
      // 更新现有节点
      existingNode.value = value;
      if (ttl != null || defaultTtl != null) {
        final effectiveTtl = ttl ?? defaultTtl!;
        existingNode.expireTime = DateTime.now().millisecondsSinceEpoch + effectiveTtl;
      }
      _moveToHead(existingNode);
      return;
    }
    
    // 创建新节点
    final newNode = LRUCacheNode<K, V>(
      key: key,
      value: value,
      expireTime: (ttl ?? defaultTtl) != null 
          ? DateTime.now().millisecondsSinceEpoch + (ttl ?? defaultTtl!)
          : null,
    );
    
    // 检查容量限制
    if (_cache.length >= maxCapacity) {
      _evictLRU();
    }
    
    _cache[key] = newNode;
    _addToHead(newNode);
  }

  /// 删除缓存项
  bool remove(K key) {
    final node = _cache[key];
    if (node == null) return false;

    _removeNode(node);
    _cache.remove(key);
    return true;
  }

  /// 检查缓存是否存在且未过期
  bool containsKey(K key) {
    final node = _cache[key];
    if (node == null) return false;

    if (node.isExpired) {
      _removeNode(node);
      _cache.remove(key);
      if (enableStats) _stats.expiredCleanCount++;
      return false;
    }

    return true;
  }

  /// 清空所有缓存
  void clear() {
    _cache.clear();
    _head.next = _tail;
    _tail.prev = _head;
    if (enableStats) _stats.reset();
  }

  /// 获取当前缓存大小
  int get length => _cache.length;

  /// 检查缓存是否为空
  bool get isEmpty => _cache.isEmpty;

  /// 检查缓存是否已满
  bool get isFull => _cache.length >= maxCapacity;

  /// 获取所有键
  Iterable<K> get keys => _cache.keys;

  /// 获取所有值
  Iterable<V> get values => _cache.values.map((node) => node.value);

  /// 获取统计信息
  LRUCacheStats get stats => _stats;

  /// 清理过期项
  int cleanupExpired() {
    final expiredKeys = <K>[];

    for (final entry in _cache.entries) {
      if (entry.value.isExpired) {
        expiredKeys.add(entry.key);
      }
    }

    for (final key in expiredKeys) {
      final node = _cache[key];
      if (node != null) {
        _removeNode(node);
        _cache.remove(key);
      }
    }

    if (enableStats) _stats.expiredCleanCount += expiredKeys.length;
    return expiredKeys.length;
  }

  /// 获取缓存项的剩余TTL
  int? getTtl(K key) {
    final node = _cache[key];
    if (node == null || node.isExpired) return null;
    return node.remainingTtl;
  }

  /// 更新缓存项的TTL
  bool updateTtl(K key, int ttl) {
    final node = _cache[key];
    if (node == null || node.isExpired) return false;

    node.expireTime = DateTime.now().millisecondsSinceEpoch + ttl;
    return true;
  }

  /// 添加节点到头部
  void _addToHead(LRUCacheNode<K, V> node) {
    node.prev = _head;
    node.next = _head.next;
    _head.next!.prev = node;
    _head.next = node;
  }

  /// 移除节点
  void _removeNode(LRUCacheNode<K, V> node) {
    node.prev!.next = node.next;
    node.next!.prev = node.prev;
  }

  /// 移动节点到头部
  void _moveToHead(LRUCacheNode<K, V> node) {
    _removeNode(node);
    _addToHead(node);
  }

  /// 淘汰最久未使用的节点
  void _evictLRU() {
    final lastNode = _tail.prev!;
    _removeNode(lastNode);
    _cache.remove(lastNode.key);
    if (enableStats) _stats.evictionCount++;
  }

  @override
  String toString() {
    return 'LRUCache{容量: $length/$maxCapacity, 统计: $_stats}';
  }
}

/// 图片缓存项信息
class ImageCacheInfo {
  /// 图片URL
  final String url;

  /// 图片宽度
  final int? width;

  /// 图片高度
  final int? height;

  /// 缓存时间
  final int cacheTime;

  /// 图片大小（字节）
  final int? sizeBytes;

  /// 是否预加载完成
  bool isPreloaded;

  /// 构造函数
  ImageCacheInfo({
    required this.url,
    this.width,
    this.height,
    this.sizeBytes,
    this.isPreloaded = false,
  }) : cacheTime = DateTime.now().millisecondsSinceEpoch;

  /// 获取缓存键
  String get cacheKey => _generateCacheKey(url, width, height);

  /// 生成缓存键
  static String _generateCacheKey(String url, int? width, int? height) {
    final buffer = StringBuffer(url);
    if (width != null) buffer.write('_w$width');
    if (height != null) buffer.write('_h$height');
    return buffer.toString();
  }
}

/// 专用图片LRU缓存
///
/// 针对图片预加载场景优化的LRU缓存实现
class ImageLRUCache extends LRUCache<String, ImageCacheInfo> {
  /// 总缓存大小（字节）
  int _totalSizeBytes = 0;

  /// 最大缓存大小（字节），null表示无限制
  final int? maxSizeBytes;

  /// 构造函数
  ImageLRUCache({
    required int maxCapacity,
    this.maxSizeBytes,
    int? defaultTtl,
    bool enableStats = true,
  }) : super(
    maxCapacity: maxCapacity,
    defaultTtl: defaultTtl,
    enableStats: enableStats,
  );

  /// 添加图片缓存
  void putImage(
    String url, {
    int? width,
    int? height,
    int? sizeBytes,
    bool isPreloaded = false,
    int? ttl,
  }) {
    final info = ImageCacheInfo(
      url: url,
      width: width,
      height: height,
      sizeBytes: sizeBytes,
      isPreloaded: isPreloaded,
    );

    final cacheKey = info.cacheKey;

    // 检查大小限制
    if (maxSizeBytes != null && sizeBytes != null) {
      _ensureSizeLimit(sizeBytes);
    }

    // 更新总大小
    final existingInfo = get(cacheKey);
    if (existingInfo != null && existingInfo.sizeBytes != null) {
      _totalSizeBytes -= existingInfo.sizeBytes!;
    }
    if (sizeBytes != null) {
      _totalSizeBytes += sizeBytes;
    }

    put(cacheKey, info, ttl: ttl);
  }

  /// 获取图片缓存信息
  ImageCacheInfo? getImage(String url, {int? width, int? height}) {
    final cacheKey = ImageCacheInfo._generateCacheKey(url, width, height);
    return get(cacheKey);
  }

  /// 检查图片是否已缓存
  bool hasImage(String url, {int? width, int? height}) {
    final cacheKey = ImageCacheInfo._generateCacheKey(url, width, height);
    return containsKey(cacheKey);
  }

  /// 移除图片缓存
  bool removeImage(String url, {int? width, int? height}) {
    final cacheKey = ImageCacheInfo._generateCacheKey(url, width, height);
    final info = get(cacheKey);
    if (info != null && info.sizeBytes != null) {
      _totalSizeBytes -= info.sizeBytes!;
    }
    return remove(cacheKey);
  }

  /// 获取当前缓存总大小（字节）
  int get totalSizeBytes => _totalSizeBytes;

  /// 获取已预加载的图片数量
  int get preloadedCount {
    return values.where((info) => info.isPreloaded).length;
  }

  /// 确保缓存大小不超过限制
  void _ensureSizeLimit(int newItemSize) {
    if (maxSizeBytes == null) return;

    while (_totalSizeBytes + newItemSize > maxSizeBytes! && !isEmpty) {
      // 找到最久未使用的节点并移除
      final lastNode = _tail.prev!;
      final info = lastNode.value;
      if (info.sizeBytes != null) {
        _totalSizeBytes -= info.sizeBytes!;
      }
      _removeNode(lastNode);
      _cache.remove(lastNode.key);
      if (enableStats) stats.evictionCount++;
    }
  }

  /// 清空缓存并重置大小计数
  @override
  void clear() {
    super.clear();
    _totalSizeBytes = 0;
  }

  @override
  String toString() {
    final sizeInfo = maxSizeBytes != null
        ? ', 大小: ${(_totalSizeBytes / 1024 / 1024).toStringAsFixed(2)}MB/${(maxSizeBytes! / 1024 / 1024).toStringAsFixed(2)}MB'
        : '';
    return 'ImageLRUCache{容量: $length/$maxCapacity$sizeInfo, 已预加载: $preloadedCount, 统计: $stats}';
  }
}
