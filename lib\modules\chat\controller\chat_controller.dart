import 'package:get/get.dart';
import 'package:rolio/common/constants/string_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/chat/service/chat_service.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'dart:async'; // Added for Timer
import 'dart:math' as math; // Added for min function

/// 聊天控制器
/// 
/// 专注于管理聊天页面的UI状态和用户交互，不包含业务逻辑
class ChatController extends GetxController {
  final ChatService _chatService;
  final GlobalState _globalState;

  ChatController({
    required ChatService chatService,
    required GlobalState globalState,
  }) : _chatService = chatService,
       _globalState = globalState;

  // === UI状态 ===
  // AI角色信息
  final RxString aiAvatarUrl = RxString('');
  final RxString aiCoverUrl = RxString('');
  final RxString aiRoleName = RxString('');
  final RxString aiDescription = RxString(''); // 添加角色描述字段
  
  // 添加新的加载状态标志
  final RxBool isInitializing = RxBool(false);
  
  // 消息加载超时相关状态
  final RxBool isLoadingMessages = RxBool(false);
  final RxBool hasMessageLoadingError = RxBool(false);
  final RxString messageLoadingErrorMessage = RxString('');
  
  // 添加标志记录用户来源
  final RxBool isFromSessionsList = RxBool(false);
  final RxBool isFromRecommendList = RxBool(false);
  
  // === 消息状态代理 ===
  RxList<Message> get messages => _chatService.messages;
  RxBool get isAiReplying => _chatService.isAiReplying;
  bool get isConnected => _chatService.isConnected;
  
  // 添加角色回复状态访问
  bool isRoleReplying(int roleId) => _chatService.isRoleReplying(roleId);
  RxMap<int, bool> get aiRoleReplyingMap => _chatService.aiRoleReplyingMap;
  
  // === 加载状态代理 - 使用服务层的状态 ===
  bool get hasMoreMessages => _chatService.hasMoreMessages;
  bool get isLoadingHistory => _chatService.isLoadingHistory;
  RxBool get isLoadingMore => _chatService.isLoadingMore;
  
  // === 角色信息状态代理 ===
  Rx<AiRole?> get currentRole => _chatService.currentRole;
  RxBool get isLoadingRoleInfo => _chatService.isLoadingRoleInfo;
  int get currentAiRoleId => _chatService.currentAiRoleId;
  
  // 添加订阅和Worker的引用，便于后续清理
  Worker? _messageLoadingWorker;
  Worker? _loadingMoreWorker;
  Worker? _roleInfoWorker;

  @override
  void onInit() {
    super.onInit();
    
    // 初始化聊天界面
    _initializeChat();
    
    // 监听角色信息变化
    _setupRoleInfoListener();
    
    // 监听消息加载状态
    _setupMessageLoadingObservers();
  }

  @override
  void onClose() {
    // 取消Worker
    _messageLoadingWorker?.dispose();

    // 取消所有订阅
    _loadingMoreWorker?.dispose();
    _roleInfoWorker?.dispose();
    
    // 清理图片预加载资源
    try {
      if (Get.isRegistered<ImagePreloader>()) {
        Get.find<ImagePreloader>().clearMemoryCache();
      }
    } catch (e) {
      LogUtil.error('清理图片预加载缓存失败: $e');
    }
    
    // 记录控制器关闭事件
    LogUtil.debug('ChatController - onClose() - 资源已释放');
    
    // 调用父类方法
    super.onClose();
  }
  
  // 监听消息加载状态
  void _setupMessageLoadingObservers() {
    // 使用isLoadingMore代替isLoadingHistory，因为isLoadingMore是RxBool类型
    _loadingMoreWorker = ever(_chatService.isLoadingMore, (bool loading) {
      // 当消息开始加载或加载完成时更新状态
      isLoadingMessages.value = loading;
      
      // 如果加载完成，清除错误状态
      if (!loading) {
        hasMessageLoadingError.value = false;
        messageLoadingErrorMessage.value = '';
      }
    });
    
    // 定期检查isLoadingHistory状态 - 使用Stream.periodic
    final loadingCheckStream = Stream.periodic(
      const Duration(milliseconds: 500),
      (count) => count,
    );

    // 监听加载状态检查流
    loadingCheckStream.listen((_) {
      final bool loading = _chatService.isLoadingHistory;
      if (isLoadingMessages.value != loading) {
        isLoadingMessages.value = loading;

        // 如果加载完成，清除错误状态
        if (!loading) {
          hasMessageLoadingError.value = false;
          messageLoadingErrorMessage.value = '';
        }
      }
    });
  }
  
  // 设置消息加载错误状态
  void setMessageLoadingError(bool hasError, String errorMessage) {
    hasMessageLoadingError.value = hasError;
    messageLoadingErrorMessage.value = errorMessage;
    
    // 如果有错误，则设置加载状态为false
    if (hasError) {
      isLoadingMessages.value = false;
    }
  }
  
  /// 检查是否有待处理消息
  /// 
  /// 返回当前角色是否有尚未收到回复的消息
  bool hasPendingMessages() {
    try {
      // 方法1：如果AI正在回复，则视为有待处理消息
      if (isAiReplying.value) {
        return true;
      }
      
      // 方法2：获取当前角色ID
      final int aiRoleId = currentAiRoleId;
      if (aiRoleId <= 0) {
        return false;
      }
      
      // 方法3：尝试使用chatService的方法检查待处理消息
      // 这里依赖于服务层，但是通过控制器封装，视图层不直接访问服务层
      try {
        dynamic service = _chatService;
        dynamic result = service.getPendingMessageIdsByRoleId(aiRoleId);
        
        if (result is List) {
          return result.isNotEmpty;
        }
      } catch (e) {
        // 如果方法不存在，忽略错误
      }
      
      // 默认情况：使用AI回复状态
      return isAiReplying.value;
    } catch (e) {
      LogUtil.error('检查待处理消息失败: $e');
      return false;
    }
  }
  
  // 加载消息
  Future<void> loadMessages() async {
    try {
      // 重置错误状态
      hasMessageLoadingError.value = false;
      messageLoadingErrorMessage.value = '';
      
      // 设置加载状态
      isLoadingMessages.value = true;
      
      // 加载当前会话的消息
      await _chatService.loadHistoryMessages(
        _chatService.currentConversationId,
        resetPage: true,
        forceRefresh: true
      );
    } catch (e) {
      LogUtil.error('加载消息失败: $e');
      setMessageLoadingError(true, 'Failed to load messages');
      
      // 使用统一的ErrorHandler处理异常
      ErrorHandler.handleException(
        AppException(
          'Failed to load messages',
          code: ErrorCodes.CHAT_HISTORY_LOAD_FAILED,
          originalError: e
        ),
      );
    } finally {
      // 完成后更新加载状态
      isLoadingMessages.value = false;
    }
  }
  
  // 初始化聊天界面
  void _initializeChat() async {
    
    // 清空现有消息
    _chatService.clearMessages();
    
    if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
      final args = Get.arguments as Map<String, dynamic>;
      
      // 从参数中提取初始角色信息（如果有的话）
      _extractInitialRoleInfo(args);
      
      // 检查是否从推荐列表进入
      if (args.containsKey('isFromRecommendList') && args['isFromRecommendList'] == true) {
        isFromRecommendList.value = true;
        isFromSessionsList.value = false;
        LogUtil.info('从推荐列表进入聊天页面，将使用推荐列表角色进行切换');
      }
      
      // 检查是否是新会话请求
      if (args.containsKey('startNewChat') && args['startNewChat'] == true) {
        LogUtil.info('检测到startNewChat参数，但前端不再创建会话，仅显示问候语');
        
        // 设置初始化状态为true
        isInitializing.value = true;
        
        // 确保有aiRoleId参数
        if (args.containsKey('aiRoleId')) {
          int aiRoleId = args['aiRoleId'] as int;
          String? greeting = args['greeting'] as String?;
          
          // 创建临时角色对象
          final initialRole = AiRole(
            id: aiRoleId,
            name: aiRoleName.value,
            avatarUrl: aiAvatarUrl.value,
            coverUrl: aiCoverUrl.value,
            description: aiDescription.value, // 确保description字段被正确传递
            tags: [],
            position: 0,
            greeting: greeting,
            // 对于新会话，不设置conversationId，确保发送消息时创建新会话
            conversationId: null, 
          );
          
          // 预先设置ChatService中的角色信息
          _chatService.presetRoleInfo(initialRole);
          
          // 更新全局状态中的当前AI角色
          _globalState.setCurrentAiRole(initialRole);
          
          // 先清空消息列表
          _chatService.clearMessages();
          LogUtil.debug('已清空消息列表');
          
          // 确保设置会话ID为0，表示需要创建新会话
          _chatService.resetConversationId();
          LogUtil.debug('已重置会话ID为0，表示需要创建新会话');
          
          // 添加通道订阅，确保能接收到消息
          try {
            // 使用switchConversation方法设置会话和订阅消息
            // 对于新会话，会话ID为0，角色ID为aiRoleId
            LogUtil.debug('通过switchConversation设置消息订阅，角色ID: $aiRoleId');
            await _chatService.switchConversation(0, aiRoleId);
            LogUtil.debug('已成功设置角色ID $aiRoleId 的消息订阅');
            
            // 重置初始化状态
            isInitializing.value = false;
          } catch (e) {
            LogUtil.error('设置消息订阅失败: $e');
            _chatService.addSystemMessage('failed to set message subscription');
            isInitializing.value = false;
          }
          
          // 删除这里的问候语显示逻辑，因为已经在switchConversation中处理过了
        } else {
          LogUtil.error('无法显示问候语：缺少aiRoleId参数');
          _chatService.addSystemMessage('failed to display greeting');
          isInitializing.value = false;
        }
      }
      // 检查是否包含conversationId
      else if (args.containsKey('conversationId')) {
        final conversationId = args['conversationId'] as int;
        int? aiRoleId;
        
        // 如果没有明确的来源标记，根据conversationId判断
        if (!isFromRecommendList.value) {
          isFromSessionsList.value = true;
          LogUtil.info('从会话列表进入聊天页面，将使用会话列表角色进行切换');
        }
        
        // 尝试从参数获取aiRoleId
        if (args.containsKey('aiRoleId')) {
          aiRoleId = args['aiRoleId'] as int;
        }
        
        // 如果没有提供aiRoleId，则需要从conversationId获取
        if (aiRoleId == null || aiRoleId <= 0) {
          LogUtil.warn('缺少有效的aiRoleId，使用默认角色ID');
          // 使用默认角色ID
          aiRoleId = 1; // 使用ID为1的默认角色
          LogUtil.debug('使用默认角色ID: $aiRoleId');
        }
        
        // 如果成功获取到aiRoleId，切换到指定会话
        if (aiRoleId != null && aiRoleId > 0) {
          // 创建临时角色对象，确保description被传递
          if (aiDescription.value.isNotEmpty) {
            final initialRole = AiRole(
              id: aiRoleId,
              name: aiRoleName.value,
              avatarUrl: aiAvatarUrl.value,
              coverUrl: aiCoverUrl.value,
              description: aiDescription.value,
              tags: [],
              position: 0,
              conversationId: conversationId
            );
            
            // 预先设置ChatService中的角色信息
            _chatService.presetRoleInfo(initialRole);
          }
          
          LogUtil.info('切换到会话: conversationId=$conversationId, aiRoleId=$aiRoleId');
          await _chatService.switchConversation(conversationId, aiRoleId);
        } else {
          LogUtil.error('无法获取有效的aiRoleId，无法切换会话');
          _chatService.addSystemMessage('failed to load conversation');
        }
      } else {
        LogUtil.warn('未提供conversationId或startNewChat=true，使用默认行为');
        _chatService.addSystemMessage('start new conversation');
      }
    } else {
      LogUtil.warn('未提供参数，使用默认行为');
      _chatService.addSystemMessage('start new conversation');
    }
  }
  
  // 从参数中提取初始角色信息
  void _extractInitialRoleInfo(Map<String, dynamic> args) {
    //LogUtil.debug('ChatController - 提取初始角色信息: $args');
    
    // 提取角色名称
    if (args.containsKey(StringsConsts.username) && args[StringsConsts.username] is String) {
      final nameString = args[StringsConsts.username] as String;
      if (nameString.isNotEmpty) {
        aiRoleName.value = nameString;
        LogUtil.debug('从参数中获取到角色名称: ${aiRoleName.value}');
      }
    }
    
    // 提取头像URL - 优先使用avatarUrl，然后是profilePic
    String? extractedAvatarUrl;
    if (args.containsKey('avatarUrl') && args['avatarUrl'] is String) {
      extractedAvatarUrl = args['avatarUrl'] as String;
      LogUtil.debug('从avatarUrl参数中获取头像URL: $extractedAvatarUrl');
    } else if (args.containsKey(StringsConsts.profilePic) && args[StringsConsts.profilePic] is String) {
      extractedAvatarUrl = args[StringsConsts.profilePic] as String;
      LogUtil.debug('从profilePic参数中获取头像URL: $extractedAvatarUrl');
    }
    
    if (extractedAvatarUrl != null && extractedAvatarUrl.isNotEmpty) {
      aiAvatarUrl.value = _sanitizeImageUrl(extractedAvatarUrl);
      LogUtil.debug('清理后的头像URL: ${aiAvatarUrl.value}');
    }
    
    // 提取封面URL - 修复封面图为null的问题
    if (args.containsKey('coverUrl') && args['coverUrl'] is String) {
      final coverUrlString = args['coverUrl'] as String;
      if (coverUrlString.isNotEmpty) {
        final sanitizedCoverUrl = _sanitizeImageUrl(coverUrlString);
        if (sanitizedCoverUrl.isNotEmpty) {
          aiCoverUrl.value = sanitizedCoverUrl;
          LogUtil.debug('从参数中获取到封面URL: ${aiCoverUrl.value}');
        } else {
          LogUtil.debug('封面URL清理后为空: $coverUrlString');
        }
      } else {
        LogUtil.debug('封面URL参数为空字符串');
      }
    } else {
      LogUtil.debug('未找到coverUrl参数或参数类型不正确');
    }
    
    // 提取角色描述
    if (args.containsKey('description') && args['description'] is String) {
      final descriptionString = args['description'] as String;
      if (descriptionString.isNotEmpty) {
        aiDescription.value = descriptionString;
        LogUtil.debug('从参数中获取到角色描述: ${aiDescription.value}');
      }
    } else {
      LogUtil.debug('未找到description参数或参数类型不正确');
    }
    
    // 如果没有头像，使用默认头像
    if (aiAvatarUrl.isEmpty) {
      aiAvatarUrl.value = _sanitizeImageUrl(StringsConsts.recommendDefaultAvatarUrl);
      LogUtil.debug('使用默认头像URL: ${aiAvatarUrl.value}');
    }
    
    // 如果没有封面，保持为空字符串，不使用默认封面
    // 这样在UI层可以根据空字符串判断不显示封面图片
    if (aiCoverUrl.isEmpty) {
      LogUtil.debug('ChatPage - 显示封面图: ${aiCoverUrl.value.isEmpty ? 'null' : aiCoverUrl.value}');
    }
    
    // 如果没有名称，使用默认名称
    if (aiRoleName.isEmpty) {
      aiRoleName.value = 'AI助手';
      LogUtil.debug('使用默认角色名称: ${aiRoleName.value}');
    }
    
    LogUtil.info('ChatController - 初始角色信息设置完成: 名称=${aiRoleName.value}, 头像=${aiAvatarUrl.value}, 封面=${aiCoverUrl.value}');
  }
  
  // 设置角色信息监听器
  void _setupRoleInfoListener() {
    // 监听角色信息变化
    _roleInfoWorker = ever(_chatService.currentRole, (AiRole? role) {
      if (role != null) {
        // 更新UI状态 - 添加空值检查和URL清理
        aiRoleName.value = role.name.isNotEmpty ? role.name : 'AI助手';
        aiAvatarUrl.value = _sanitizeImageUrl(role.avatarUrl);
        
        // 更新角色描述
        if (role.description.isNotEmpty) {
          aiDescription.value = role.description;
          LogUtil.debug('从角色信息更新角色描述: ${aiDescription.value.substring(0, math.min(30, aiDescription.value.length))}...');
        }
        
        // 处理封面URL - 只有当角色有有效的封面URL时才更新
        String sanitizedCoverUrl = _sanitizeImageUrl(role.coverUrl);
        if (sanitizedCoverUrl.isNotEmpty) {
          // 只有在有新的有效封面URL时才更新
          aiCoverUrl.value = sanitizedCoverUrl;
          //LogUtil.debug('ChatController - 更新封面URL: $sanitizedCoverUrl');
        } else if (role.coverUrl.isNotEmpty) {
          // 如果原始URL不为空但清理后为空，记录警告
          LogUtil.warn('ChatController - 角色封面URL格式无效: ${role.coverUrl}');
        } else if (aiCoverUrl.value.isEmpty) {
          // 如果当前没有封面且获取的也是空，尝试从初始参数获取
          if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
            final args = Get.arguments as Map<String, dynamic>;
            if (args.containsKey('coverUrl') && args['coverUrl'] is String) {
              final initialCoverUrl = _sanitizeImageUrl(args['coverUrl'] as String);
              if (initialCoverUrl.isNotEmpty) {
                aiCoverUrl.value = initialCoverUrl;
                //LogUtil.debug('ChatController - 使用初始参数封面URL: $initialCoverUrl');
              }
            }
          }
        }
        
        //LogUtil.debug('ChatController - 角色信息更新: 名称=${aiRoleName.value}, 头像=${aiAvatarUrl.value}, 封面=${aiCoverUrl.value}');
        
        // 更新页面参数
        _updatePageArguments(role);
        
        // 更新全局状态中的当前AI角色
        _globalState.setCurrentAiRole(role);
        
        // 预加载头像图片
        _preloadRoleImages(role);
        
        // 预加载相邻角色的图片，避免切换时出现黑屏
        _preloadAdjacentRolesImages(role.id);
        
        // 通知UI更新
        update();
      }
    });
  }
  
  // 预加载角色图片
  void _preloadRoleImages(AiRole role) {
    // 使用GetX依赖注入的图片预加载器
    final imagePreloader = Get.find<ImagePreloader>();
    
    // 清理URL
    final cleanAvatarUrl = _sanitizeImageUrl(role.avatarUrl);
    final cleanCoverUrl = _sanitizeImageUrl(role.coverUrl);
    
    // 预加载头像 - 高优先级
    if (cleanAvatarUrl.isNotEmpty) {
      imagePreloader.preloadImage(
        cleanAvatarUrl,
        priority: ImagePreloadPriority.high,
      );
      //LogUtil.debug('ChatController - 预加载头像: ${cleanAvatarUrl}');
    }

    // 预加载封面图片 - 高优先级
    if (cleanCoverUrl.isNotEmpty) {
      imagePreloader.preloadImage(
        cleanCoverUrl,
        priority: ImagePreloadPriority.high,
      );
      //LogUtil.debug('ChatController - 预加载封面图片: ${cleanCoverUrl}');
    }
  }
  
  // 预加载相邻角色的图片
  Future<void> _preloadAdjacentRolesImages(int currentRoleId) async {
    try {
      // 使用GetX依赖注入的图片预加载器
      final imagePreloader = Get.find<ImagePreloader>();
      
      // 获取下一个角色的基本信息
      final nextRole = await _chatService.getNextRole(currentRoleId, isFromSessionsList.value);
      if (nextRole != null) {
        // 清理URL
        final cleanAvatarUrl = _sanitizeImageUrl(nextRole.avatarUrl);
        final cleanCoverUrl = _sanitizeImageUrl(nextRole.coverUrl);
        
        // 预加载下一个角色的封面图
        if (cleanCoverUrl.isNotEmpty && !imagePreloader.isImagePreloaded(cleanCoverUrl)) {
          imagePreloader.preloadImage(
            cleanCoverUrl,
            priority: ImagePreloadPriority.high,
          );
          //LogUtil.debug('ChatController - 预加载下一个角色封面: ${cleanCoverUrl}');
        }
        
        // 预加载下一个角色的头像
        if (cleanAvatarUrl.isNotEmpty && !imagePreloader.isImagePreloaded(cleanAvatarUrl)) {
          imagePreloader.preloadImage(
            cleanAvatarUrl,
            priority: ImagePreloadPriority.high,
          );
          //LogUtil.debug('ChatController - 预加载下一个角色头像: ${cleanAvatarUrl}');
        }
      }
      
      // 获取上一个角色的基本信息
      final prevRole = await _chatService.getPreviousRole(currentRoleId, isFromSessionsList.value);
      if (prevRole != null) {
        // 清理URL
        final cleanAvatarUrl = _sanitizeImageUrl(prevRole.avatarUrl);
        final cleanCoverUrl = _sanitizeImageUrl(prevRole.coverUrl);
        
        // 预加载上一个角色的封面图
        if (cleanCoverUrl.isNotEmpty && !imagePreloader.isImagePreloaded(cleanCoverUrl)) {
          imagePreloader.preloadImage(
            cleanCoverUrl,
            priority: ImagePreloadPriority.high,
          );
          //LogUtil.debug('ChatController - 预加载上一个角色封面: ${cleanCoverUrl}');
        }
        
        // 预加载上一个角色的头像
        if (cleanAvatarUrl.isNotEmpty && !imagePreloader.isImagePreloaded(cleanAvatarUrl)) {
          imagePreloader.preloadImage(
            cleanAvatarUrl,
            priority: ImagePreloadPriority.high,
          );
          //LogUtil.debug('ChatController - 预加载上一个角色头像: ${cleanAvatarUrl}');
        }
      }
    } catch (e) {
      LogUtil.error('预加载相邻角色图片失败: $e');
    }
  }
  
  // 更新页面参数
  void _updatePageArguments(AiRole role) {
    if (Get.arguments != null && Get.arguments is Map<String, dynamic>) {
      final args = Get.arguments as Map<String, dynamic>;
      args[StringsConsts.username] = role.name;
      args['avatarUrl'] = role.avatarUrl;
      args['coverUrl'] = role.coverUrl;
      args['description'] = role.description; // 更新description参数
    }
  }
  
  // === UI交互方法 ===
  
  // 获取当前会话ID
  int get conversationId => _chatService.currentConversationId;
  
  // 获取当前AI角色ID
  int get aiRoleId => _chatService.currentAiRoleId;
  
  // 检查是否为AI聊天
  bool get isAiChat => true; // 始终为true，因为我们只处理AI聊天

  /// 发送文本消息
  ///
  /// [lastMessage] 消息内容
  /// [receiverUserId] 接收者ID
  /// [groupId] 群组ID（可选）
  /// [isGroupChat] 是否为群聊
  Future<void> sendTextMessage({
    required String lastMessage,
    required String receiverUserId,
    String? groupId,
    bool isGroupChat = false,
  }) async {
    try {
      // 安全检查：确保控制器仍然存在（未被销毁）
      if (!GetInstance().isRegistered<ChatController>()) {
        LogUtil.warn('发送消息失败：ChatController已被释放');
        return;
      }
      
      // 检查输入参数
      if (lastMessage.trim().isEmpty) {
        LogUtil.warn('消息内容为空，取消发送');
        return;
      }
      
      if (receiverUserId.isEmpty) {
        LogUtil.error('接收者ID为空，无法发送消息');
        ErrorHandler.handleException(
          AppException('接收者ID不能为空', code: ErrorCodes.BUSINESS_ERROR),
        );
        return;
      }
      
      // 发送消息
      await _chatService.sendMessage(lastMessage);
      
      // 不需要刷新消息列表，因为发送消息时会自动添加到列表中
      
    } catch (e) {
      LogUtil.error('发送消息失败: $e');
      
      // 使用统一的ErrorHandler处理异常
      ErrorHandler.handleException(
        AppException('failed to send message', code: ErrorCodes.CHAT_SEND_FAILED, originalError: e),
      );
      
      // 重新抛出异常，让调用者可以处理
      rethrow;
    }
  }

  /// 切换角色
  Future<void> switchRole({bool isNext = true}) async {
    try {
      LogUtil.info('开始切换到${isNext ? "下一个" : "上一个"}角色');
      isLoadingRoleInfo.value = true;
      
      // 确定角色来源
      bool fromSessionsList = isFromSessionsList.value;
      bool fromRecommendList = isFromRecommendList.value;
      
      // 记录日志
      if (fromSessionsList) {
        LogUtil.info('从会话列表获取${isNext ? "下一个" : "上一个"}角色');
      } else if (fromRecommendList) {
        LogUtil.info('从推荐列表获取${isNext ? "下一个" : "上一个"}角色');
      } else {
        LogUtil.info('从默认列表获取${isNext ? "下一个" : "上一个"}角色');
      }
      
      // 使用ChatService切换角色，传递来源标志
      final role = await _chatService.switchRole(
        isNext: isNext, 
        fromSessionsList: fromSessionsList,
        fromRecommendList: fromRecommendList
      );
      
      if (role != null) {
        LogUtil.info('已切换到${isNext ? "下一个" : "上一个"}角色: ${role.name}');
      } else {
        LogUtil.warn('切换角色失败或没有${isNext ? "下一个" : "上一个"}角色');
      }
    } catch (e) {
      LogUtil.error('切换到${isNext ? "下一个" : "上一个"}角色失败: $e');
      ErrorHandler.handleException(e);
    } finally {
      isLoadingRoleInfo.value = false;
    }
  }
  
  /// 切换到下一个角色
  Future<void> switchToNextRole() async {
    await switchRole(isNext: true);
  }
  
  /// 切换到上一个角色
  Future<void> switchToPreviousRole() async {
    await switchRole(isNext: false);
  }
  
  // 加载更多历史消息 - UI调用接口
  Future<void> loadMoreMessages() async {
    if (!isLoadingHistory && hasMoreMessages) {
      await _chatService.loadHistoryMessages(conversationId);
    }
  }

  // 清理和验证图片URL
  String _sanitizeImageUrl(String? url) {
    if (url == null || url.isEmpty || url.trim().isEmpty) {
      //LogUtil.debug('ChatController - 图片URL为空或null: $url');
      return '';
    }
    
    // 清理URL字符串
    String cleaned = url.trim();
    
    // 检查是否为"null"字符串
    if (cleaned.toLowerCase() == 'null') {
      //LogUtil.debug('ChatController - 图片URL为字符串"null": $url');
      return '';
    }
    
    // 移除可能的反引号或其他特殊字符
    cleaned = cleaned.replaceAll(RegExp(r'^`+|`+$'), '');
    cleaned = cleaned.replaceAll(RegExp(r'^\"+|\"+$'), '');
    cleaned = cleaned.trim();
    
    // 再次检查清理后是否为空
    if (cleaned.isEmpty) {
      //LogUtil.debug('ChatController - 图片URL清理后为空: $url');
      return '';
    }
    
    // 对于没有协议前缀的URL，添加https前缀
    if (!cleaned.startsWith('http://') && !cleaned.startsWith('https://') && 
        !cleaned.startsWith('/') && !cleaned.startsWith('./') && !cleaned.startsWith('../')) {
      cleaned = 'https://' + cleaned;
      //LogUtil.debug('ChatController - 自动添加https前缀: $cleaned');
    }
    
    // 不再验证URL格式和内容，假设所有URL都是有效的
    //LogUtil.debug('ChatController - 清理后的图片URL: $cleaned');
    return cleaned;
  }

}

