import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/modules/role/service/role_service.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'dart:async';  // 导入用于StreamSubscription

/// 页面状态枚举
enum RolePageState {
  loading,      // 加载中
  loaded,       // 加载完成
  error,        // 错误状态
  favoriteLoading, // 收藏操作中
}

/// 图片预加载状态类
class ImagePreloadState {
  final bool isCoverPreloaded;
  final bool isAvatarPreloaded;
  final Set<String> preloadingUrls;
  final Map<String, int> retryCount;

  const ImagePreloadState({
    this.isCoverPreloaded = false,
    this.isAvatarPreloaded = false,
    this.preloadingUrls = const {},
    this.retryCount = const {},
  });

  ImagePreloadState copyWith({
    bool? isCoverPreloaded,
    bool? isAvatarPreloaded,
    Set<String>? preloadingUrls,
    Map<String, int>? retryCount,
  }) {
    return ImagePreloadState(
      isCoverPreloaded: isCoverPreloaded ?? this.isCoverPreloaded,
      isAvatarPreloaded: isAvatarPreloaded ?? this.isAvatarPreloaded,
      preloadingUrls: preloadingUrls ?? this.preloadingUrls,
      retryCount: retryCount ?? this.retryCount,
    );
  }
}

/// 角色控制器
/// 负责管理角色详情页的UI状态和业务逻辑
class RoleController extends GetxController {
  // 服务
  late final RoleService roleService;

  // 核心状态变量
  final Rx<AiRole?> currentRole = Rx<AiRole?>(null);
  final Rx<RolePageState> pageState = RolePageState.loading.obs;
  final RxString errorMessage = ''.obs;
  final Rx<ImagePreloadState> imagePreloadState = const ImagePreloadState().obs;

  // 图片预加载器
  ImagePreloader get _imagePreloader => Get.find<ImagePreloader>();

  // Workers列表
  final List<Worker> _workers = [];

  // 常量
  static const int maxPreloadRetry = 2;
  static const int maxRetry = 3;
  int _retryCount = 0;

  // Computed属性
  bool get isLoading => pageState.value == RolePageState.loading;
  bool get isFavoriteLoading => pageState.value == RolePageState.favoriteLoading;
  bool get hasError => pageState.value == RolePageState.error;
  bool get isCoverPreloaded => imagePreloadState.value.isCoverPreloaded;
  
  @override
  void onInit() {
    super.onInit();

    // 获取服务实例
    roleService = Get.find<RoleService>();

    // 设置GetX Workers
    _setupWorkers();

    // 获取路由参数
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      final roleId = arguments['roleId'];
      if (roleId != null && roleId is int) {
        // 获取角色详情（包含收藏信息）
        getRoleDetail(roleId);
      } else {
        LogUtil.error('角色ID无效: $roleId');
        _setError('无效的角色ID');
      }
    } else {
      LogUtil.error('未提供角色ID参数');
      _setError('未提供角色ID');
    }
  }

  /// 设置GetX Workers管理状态依赖
  void _setupWorkers() {
    // 监听角色数据变化，自动触发图片预加载
    final roleWorker = ever(currentRole, (AiRole? role) {
      if (role != null) {
        _handleRoleDataChanged(role);
      }
    });
    _workers.add(roleWorker);

    // 监听图片预加载状态变化
    final imageWorker = ever(imagePreloadState, (ImagePreloadState state) {
      LogUtil.debug('图片预加载状态更新: 封面=${state.isCoverPreloaded}, 头像=${state.isAvatarPreloaded}');
    });
    _workers.add(imageWorker);

    // 防抖处理重试逻辑
    final retryWorker = debounce(
      pageState,
      (RolePageState state) {
        if (state == RolePageState.error && _retryCount < maxRetry) {
          _handleAutoRetry();
        }
      },
      time: const Duration(seconds: 2),
    );
    _workers.add(retryWorker);
  }
  
  /// 打开搜索页面
  void goToSearch() {
    LogUtil.debug('打开搜索页面');
    Get.toNamed('/role/search');
  }

  /// 获取角色详情
  ///
  /// 通过RoleService获取角色详细信息
  /// [roleId] 角色ID
  /// [forceRefresh] 是否强制刷新，不使用缓存
  Future<void> getRoleDetail(int roleId, {bool forceRefresh = false}) async {
    try {
      pageState.value = RolePageState.loading;
      errorMessage.value = '';

      LogUtil.info('获取角色详情: ID=$roleId, forceRefresh=$forceRefresh');

      // 从RoleService获取完整角色详情
      final role = await roleService.getRoleDetail(roleId, forceRefresh: forceRefresh);

      if (role != null) {
        // 更新当前角色 - 这会触发Worker自动处理图片预加载
        currentRole.value = role;
        _retryCount = 0; // 成功后重置重试次数
        pageState.value = RolePageState.loaded;
        LogUtil.debug('成功获取角色详情: ${role.name}, 收藏状态: ${role.isFavorited}');
      } else {
        LogUtil.error('未找到角色: ID=$roleId');
        _setError('Role not found');
      }
    } catch (e) {
      LogUtil.error('获取角色详情失败: $e');
      _setError('Failed to load role details');
      ErrorHandler.handleException(e);
    }
  }

  /// 处理角色数据变化 - 由Worker自动调用
  void _handleRoleDataChanged(AiRole role) {
    LogUtil.debug('角色数据变化，开始预加载图片: ${role.name}');

    // 重置图片预加载状态
    imagePreloadState.value = const ImagePreloadState();

    // 开始预加载图片
    _preloadRoleImages(role);
  }

  /// 处理自动重试 - 由Worker自动调用
  void _handleAutoRetry() {
    final arguments = Get.arguments;
    if (arguments != null && arguments is Map<String, dynamic>) {
      final roleId = arguments['roleId'];
      if (roleId != null && roleId is int) {
        _retryCount++;
        LogUtil.info('自动重试获取角色详情，第$_retryCount次: ID=$roleId');
        getRoleDetail(roleId, forceRefresh: true);
      }
    }
  }
  
  /// 重试获取角色详情
  Future<void> retryGetRoleDetail() async {
    final role = currentRole.value;
    if (role != null) {
      _retryCount++;
      LogUtil.info('手动重试获取角色详情，第$_retryCount次: ID=${role.id}');
      await getRoleDetail(role.id, forceRefresh: true);
    } else {
      final arguments = Get.arguments;
      if (arguments != null && arguments is Map<String, dynamic>) {
        final roleId = arguments['roleId'];
        if (roleId != null && roleId is int) {
          _retryCount++;
          LogUtil.info('从路由参数手动重试获取角色详情，第$_retryCount次: ID=$roleId');
          await getRoleDetail(roleId, forceRefresh: true);
        } else {
          LogUtil.error('无法重试，当前没有角色ID');
          ToastUtil.error('Cannot retry, no role ID available');
        }
      } else {
        LogUtil.error('无法重试，当前没有角色ID');
        ToastUtil.error('Cannot retry, no role ID available');
      }
    }
  }

  /// 切换收藏状态
  ///
  /// 如果角色已收藏则取消收藏，否则收藏角色
  Future<void> toggleFavorite() async {
    try {
      final role = currentRole.value;
      if (role == null) {
        LogUtil.warn('当前没有角色，无法切换收藏状态');
        return;
      }

      pageState.value = RolePageState.favoriteLoading;
      LogUtil.debug('切换收藏状态，当前状态: ${role.isFavorited}, 角色ID: ${role.id}');

      // 调用服务层统一方法切换收藏状态
      final newState = await roleService.toggleFavorite(role.id);

      if (newState == null) {
        // 操作失败，显示错误信息
        ToastUtil.error('Failed to change favorite status');
        pageState.value = RolePageState.loaded;
      } else {
        // 操作成功，显示成功提示
        if (newState) {
          ToastUtil.success('Added to favorites');
        } else {
          ToastUtil.success('Removed from favorites');
        }

        // 服务层已经更新了状态，这里确保UI状态一致
        if (currentRole.value != null) {
          currentRole.value = currentRole.value!.copyWith(
            isFavorited: newState,
            favoritedAt: newState ? DateTime.now() : null,
          );
        }
        pageState.value = RolePageState.loaded;
      }
    } catch (e) {
      LogUtil.error('切换收藏状态异常: $e');
      ToastUtil.error('An error occurred while updating favorite status');
      ErrorHandler.handleException(e);
      pageState.value = RolePageState.loaded;
    }
  }
  
  /// 预加载角色图片 - 由Worker自动调用
  void _preloadRoleImages(AiRole role) {
    final currentState = imagePreloadState.value;
    final newPreloadingUrls = Set<String>.from(currentState.preloadingUrls);
    final newRetryCount = Map<String, int>.from(currentState.retryCount);

    // 预加载封面图
    if (role.coverUrl.isNotEmpty) {
      final isCoverPreloaded = _imagePreloader.isImagePreloaded(role.coverUrl);

      if (!isCoverPreloaded && !newPreloadingUrls.contains(role.coverUrl)) {
        LogUtil.debug('开始预加载封面图: ${role.coverUrl}');
        newPreloadingUrls.add(role.coverUrl);

        _imagePreloader.preloadImage(
          role.coverUrl,
          priority: ImagePreloadPriority.high,
          onComplete: (success) => _handleImagePreloadComplete(
            role.coverUrl,
            success,
            isHighPriority: true,
            isCover: true,
          ),
        );
      }

      // 更新封面预加载状态
      imagePreloadState.value = currentState.copyWith(
        isCoverPreloaded: isCoverPreloaded,
        preloadingUrls: newPreloadingUrls,
        retryCount: newRetryCount,
      );
    }

    // 预加载头像图片
    if (role.avatarUrl.isNotEmpty) {
      final isAvatarPreloaded = _imagePreloader.isImagePreloaded(role.avatarUrl);

      if (!isAvatarPreloaded && !newPreloadingUrls.contains(role.avatarUrl)) {
        LogUtil.debug('开始预加载头像: ${role.avatarUrl}');
        newPreloadingUrls.add(role.avatarUrl);

        _imagePreloader.preloadImage(
          role.avatarUrl,
          priority: ImagePreloadPriority.medium,
          onComplete: (success) => _handleImagePreloadComplete(
            role.avatarUrl,
            success,
            isHighPriority: false,
            isCover: false,
          ),
        );
      }

      // 更新头像预加载状态
      imagePreloadState.value = imagePreloadState.value.copyWith(
        isAvatarPreloaded: isAvatarPreloaded,
        preloadingUrls: newPreloadingUrls,
        retryCount: newRetryCount,
      );
    }
  }
  
  /// 处理图片预加载完成
  void _handleImagePreloadComplete(String imageUrl, bool success, {
    required bool isHighPriority,
    required bool isCover,
  }) {
    final currentState = imagePreloadState.value;
    final newPreloadingUrls = Set<String>.from(currentState.preloadingUrls);
    final newRetryCount = Map<String, int>.from(currentState.retryCount);

    // 从预加载集合中移除
    newPreloadingUrls.remove(imageUrl);

    if (success) {
      LogUtil.debug('图片预加载成功: $imageUrl');
      // 重置重试计数
      newRetryCount.remove(imageUrl);

      // 更新预加载状态
      imagePreloadState.value = currentState.copyWith(
        isCoverPreloaded: isCover ? true : currentState.isCoverPreloaded,
        isAvatarPreloaded: !isCover ? true : currentState.isAvatarPreloaded,
        preloadingUrls: newPreloadingUrls,
        retryCount: newRetryCount,
      );
    } else {
      // 预加载失败，尝试重试
      final retryCount = newRetryCount[imageUrl] ?? 0;
      if (retryCount < maxPreloadRetry) {
        newRetryCount[imageUrl] = retryCount + 1;
        LogUtil.debug('图片预加载失败，准备第${retryCount + 1}次重试: $imageUrl');

        // 更新状态
        imagePreloadState.value = currentState.copyWith(
          preloadingUrls: newPreloadingUrls,
          retryCount: newRetryCount,
        );

        // 延迟重试
        Future.delayed(Duration(milliseconds: 500 * (retryCount + 1)), () {
          if (!Get.isRegistered<RoleController>()) return;

          final updatedState = imagePreloadState.value;
          final updatedPreloadingUrls = Set<String>.from(updatedState.preloadingUrls);
          updatedPreloadingUrls.add(imageUrl);

          imagePreloadState.value = updatedState.copyWith(
            preloadingUrls: updatedPreloadingUrls,
          );

          _imagePreloader.preloadImage(
            imageUrl,
            priority: isHighPriority ? ImagePreloadPriority.high : ImagePreloadPriority.medium,
            onComplete: (success) => _handleImagePreloadComplete(
              imageUrl,
              success,
              isHighPriority: isHighPriority,
              isCover: isCover,
            ),
          );
        });
      } else {
        LogUtil.warn('图片预加载失败，已达到最大重试次数: $imageUrl');
        newRetryCount.remove(imageUrl);
        imagePreloadState.value = currentState.copyWith(
          preloadingUrls: newPreloadingUrls,
          retryCount: newRetryCount,
        );
      }
    }
  }
  
  /// 设置错误状态
  void _setError(String message) {
    pageState.value = RolePageState.error;
    errorMessage.value = message;
  }

  @override
  void onClose() {
    // 清理所有Workers
    for (final worker in _workers) {
      worker.dispose();
    }
    _workers.clear();

    // 重置状态
    pageState.value = RolePageState.loading;
    errorMessage.value = '';
    imagePreloadState.value = const ImagePreloadState();
    currentRole.value = null;
    _retryCount = 0;

    LogUtil.debug('RoleController: 已释放资源');
    super.onClose();
  }
}
